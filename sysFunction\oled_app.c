#include "oled_app.h"
#include "adc_app.h"
#include "rtc_app.h"

int Oled_Printf(uint8_t x, uint8_t y, const char *format, ...)
{
	char buffer[128];
	va_list arg;
	int len;

	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	OLED_ShowStr(x, y, buffer, 16); 
	return len;
}

void oled_task(void)
{
    static sampling_state_t last_state = SAMPLING_IDLE;  // 记录上次状态
    static uint32_t last_cycle_ms = 0;                   // 记录上次采样周期
    static float last_display_voltage = -1.0f;          // 记录上次显示的电压值

    // 检查状态或采样周期是否发生变化，如果变化则清屏
    if (g_adc_control.state != last_state || g_adc_control.cycle_ms != last_cycle_ms) {
        OLED_Clear();  // 清屏操作
        last_state = g_adc_control.state;      // 更新状态记录
        last_cycle_ms = g_adc_control.cycle_ms; // 更新周期记录
        last_display_voltage = -1.0f;          // 重置电压记录，强制刷新
    }

    if (g_adc_control.state == SAMPLING_ACTIVE) {
        // 采样状态：显示时间和电压值（16号字体，2行显示）
        RTC_TimeTypeDef current_time = {0};
        RTC_DateTypeDef current_date = {0};

        // 获取当前时间
        rtc_get_time_info(&current_time, &current_date);

        Oled_Printf(32, 0, "%02d:%02d:%02d",
                   current_time.Hours,
                   current_time.Minutes,
                   current_time.Seconds);

        // 检查电压值是否发生变化，如果变化则清除第二行区域
        if (last_display_voltage != g_adc_control.display_voltage) {
            // 清除第二行显示区域
            for (uint8_t page = 2; page <= 3; page++) {
                OLED_Set_Position(0, page);
                for (uint8_t x = 0; x < 128; x++) {
                    OLED_Write_data(0);
                }
            }
            last_display_voltage = g_adc_control.display_voltage;
        }

        Oled_Printf(36, 2, "%.2fV", g_adc_control.display_voltage);


    } else {
        Oled_Printf(20, 1, "system idle");
    }

}






