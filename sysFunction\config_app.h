#ifndef __CONFIG_APP_H_
#define __CONFIG_APP_H_

#include "mydefine.h"
#include "sampling_types.h"  // 包含采样相关类型定义

// 配置参数结构体
typedef struct {
    float ratio;                    // 变比参数 (0-100)
    float limit;                    // 阈值参数 (0-200)
    sampling_cycle_t sample_cycle;  // 采样周期 (5s/10s/15s)
} config_params_t;

// 配置管理状态枚举
typedef enum {
    CONFIG_OK = 0,
    CONFIG_ERROR,
    CONFIG_FILE_NOT_FOUND,
    CONFIG_INVALID_PARAM,
    CONFIG_FLASH_ERROR
} config_status_t;

// INI 文件解析相关
#define CONFIG_FILE_NAME "config.ini"
#define CONFIG_MAX_LINE_LENGTH 128
#define CONFIG_MAX_SECTION_NAME 32
#define CONFIG_MAX_KEY_NAME 32
#define CONFIG_MAX_VALUE_NAME 32

// 参数范围定义
#define RATIO_MIN 0.0f
#define RATIO_MAX 100.0f
#define LIMIT_MIN 0.0f
#define LIMIT_MAX 200.0f

// Flash 存储相关
#define CONFIG_FLASH_FILE "config.dat"

// 全局配置参数
extern config_params_t g_config_params;

// 函数声明
void config_app_init(void);                                    // 配置模块初始化
config_status_t config_load_from_sd(void);                     // 从SD卡加载配置文件
config_status_t config_safe_sd_test(void);                     // SD卡文件操作安全测试
config_status_t config_display_from_sd(void);                  // 从SD卡显示配置文件内容（只读模式）
config_status_t config_save_to_flash(void);                    // 保存配置到Flash
config_status_t config_save_to_sd(void);                       // 保存配置到SD卡config.ini文件
config_status_t config_load_from_flash(void);                  // 从Flash加载配置
config_status_t config_ensure_ini_file(void);                  // 确保config.ini文件存在
config_status_t config_set_ratio(float ratio);                 // 设置变比参数
config_status_t config_set_limit(float limit);                 // 设置阈值参数
config_status_t config_set_sample_cycle(sampling_cycle_t cycle); // 设置采样周期参数
float config_get_ratio(void);                                  // 获取变比参数
float config_get_limit(void);                                  // 获取阈值参数
sampling_cycle_t config_get_sample_cycle(void);                // 获取采样周期参数
config_status_t config_validate_ratio(float ratio);            // 验证变比参数
config_status_t config_validate_limit(float limit);            // 验证阈值参数

// INI 文件解析函数
config_status_t parse_ini_file(const char *file_content, config_params_t *params);
config_status_t parse_ini_line(const char *line, char *section, char *key, char *value);
void trim_string(char *str);                                   // 去除字符串前后空格

#endif
