#ifndef __SELFTEST_APP_H_
#define __SELFTEST_APP_H_

// 直接包含需要的头文件，避免循环包含
#include "stdint.h"
#include "stdio.h"

// 自检结果枚举
typedef enum {
    SELFTEST_OK = 0,
    SELFTEST_FLASH_ERROR,
    SELFTEST_SD_ERROR,
    SELFTEST_RTC_ERROR
} selftest_result_t;

// 自检项目结构体
typedef struct {
    uint8_t flash_ok;       // Flash检测结果
    uint8_t sd_ok;          // SD卡检测结果
    uint8_t rtc_ok;         // RTC检测结果
    uint32_t flash_id;      // Flash ID
    uint32_t sd_capacity;   // SD卡容量(KB)
    char rtc_time[32];      // RTC时间字符串
} selftest_info_t;

// 设备ID相关定义
#define DEVICE_ID_FLASH_FILE "device_id.dat"
#define DEFAULT_TEAM_NUMBER "000"  // 默认队伍编号（用于检测Flash读取是否成功）

// 设备ID结构体
typedef struct {
    char team_number[16];       // 队伍编号（扩大以容纳新的编号格式）
    uint32_t power_on_count;    // 上电次数计数器
    uint8_t initialized;        // 初始化标志
} device_info_t;

// 全局设备信息
extern device_info_t g_device_info;

// 自检函数声明
selftest_result_t selftest_run_all(void);                          // 运行完整自检
selftest_result_t selftest_check_flash(selftest_info_t *info);     // 检测Flash
selftest_result_t selftest_check_flash_simple(selftest_info_t *info); // 简化Flash检测
selftest_result_t selftest_check_sd(selftest_info_t *info);        // 检测SD卡
selftest_result_t selftest_check_rtc(selftest_info_t *info);       // 检测RTC
void selftest_print_results(const selftest_info_t *info);          // 打印自检结果
void selftest_format_output(const selftest_info_t *info, char *buffer, size_t buffer_size); // 格式化输出

// 设备ID管理函数声明
void device_id_init(void);                      // 设备ID初始化
void device_id_load_from_flash(void);           // 从Flash加载设备ID
void device_id_save_to_flash(void);             // 保存设备ID到Flash
void device_id_print(void);                     // 打印设备ID
void system_startup_sequence(void);             // 系统启动序列

#endif
