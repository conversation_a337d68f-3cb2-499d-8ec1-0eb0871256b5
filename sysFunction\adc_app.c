#include "adc_app.h"
#include "tim.h"
#include "config_app.h"
#include "rtc_app.h"
#include "led_app.h"
#include "sd_app.h"

uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; //原始测量数据存这里
__IO uint32_t adc_val; //平均后的数字
__IO float voltage; //最终电压值

#define VOLTAGE_FILTER_SIZE 16 //保存16次历史数据让显示稳定
static float voltage_filter_buffer[VOLTAGE_FILTER_SIZE] = {0};
static uint8_t voltage_filter_index = 0;
static uint8_t voltage_filter_filled = 0;

#define MEDIAN_FILTER_SIZE 5 //去掉奇怪的测量值
static float median_filter_buffer[MEDIAN_FILTER_SIZE] = {0};
static uint8_t median_filter_index = 0;

adc_control_t g_adc_control = { //电压测量总控制器
    .state = SAMPLING_IDLE, //开始时待机
    .cycle = CYCLE_5S, //默认5秒测一次
    .last_sample_time = 0, //上次测量时间
    .cycle_ms = 5000, //5秒=5000毫秒
    .processed_voltage = 0.0f, //测到的电压
    .over_limit = 0, //是否超限
    .led1_blink_state = 0 //LED闪烁状态
};

uint8_t g_hide_mode_enabled = 0; //数据加密开关

static float voltage_median_filter(float new_value) //去掉奇怪的电压值
{
    median_filter_buffer[median_filter_index] = new_value; //存新值
    median_filter_index = (median_filter_index + 1) % MEDIAN_FILTER_SIZE;

    float temp_buffer[MEDIAN_FILTER_SIZE]; //复制数据来排序
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE; i++) {
        temp_buffer[i] = median_filter_buffer[i];
    }

    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE - 1; i++) { //冒泡排序
        for (uint8_t j = 0; j < MEDIAN_FILTER_SIZE - 1 - i; j++) {
            if (temp_buffer[j] > temp_buffer[j + 1]) {
                float temp = temp_buffer[j];
                temp_buffer[j] = temp_buffer[j + 1];
                temp_buffer[j + 1] = temp;
            }
        }
    }

    return temp_buffer[MEDIAN_FILTER_SIZE / 2]; //返回中间值
}

static float voltage_sliding_average_filter(float new_voltage) //让显示稳定不跳
{
    voltage_filter_buffer[voltage_filter_index] = new_voltage; //存新值
    voltage_filter_index = (voltage_filter_index + 1) % VOLTAGE_FILTER_SIZE;

    if (!voltage_filter_filled && voltage_filter_index == 0) { //记录是否存满
        voltage_filter_filled = 1;
    }

    float sum = 0.0f; //计算平均值
    uint8_t count = voltage_filter_filled ? VOLTAGE_FILTER_SIZE : voltage_filter_index;

    for (uint8_t i = 0; i < count; i++) {
        sum += voltage_filter_buffer[i];
    }

    return sum / count; //返回平均值
}

void adc_dma_init(void) //启动电压测量系统
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_dma_buffer, ADC_DMA_BUFFER_SIZE); //开始测量
}

void adc_task(void) //电压测量主函数，每0.1秒跑一次
{
    uint32_t adc_sum = 0; //累加测量结果

    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) { //把所有测量值加起来
        adc_sum += adc_dma_buffer[i];
    }

    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE; //算平均值

    float raw_voltage = ((float)adc_val * 3.3f) / 4096.0f; //转成电压值

    float median_filtered = voltage_median_filter(raw_voltage); //去异常值
    voltage = voltage_sliding_average_filter(median_filtered); //平滑处理

    float ratio = config_get_ratio(); //用户设的比例
    g_adc_control.processed_voltage = voltage * ratio;

    adc_check_over_limit(); //检查是否超限

    if (g_adc_control.state == SAMPLING_ACTIVE) { //如果在定时测量
        adc_process_sample();
    }
}

void adc_control_init(void) //初始化测量系统
{
    g_adc_control.state = SAMPLING_IDLE; //待机状态
    g_adc_control.last_sample_time = 0; //清空时间
    g_adc_control.processed_voltage = 0.0f; //清空电压
    g_adc_control.display_voltage = 0.0f; //清空显示值
    g_adc_control.over_limit = 0; //清除超限
    g_adc_control.led1_blink_state = 0; //清除LED状态

    for (uint8_t i = 0; i < VOLTAGE_FILTER_SIZE; i++) { //清空历史数据
        voltage_filter_buffer[i] = 0.0f;
    }
    voltage_filter_index = 0;
    voltage_filter_filled = 0;

    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE; i++) { //清空过滤数据
        median_filter_buffer[i] = 0.0f;
    }
    median_filter_index = 0;

    sampling_cycle_t saved_cycle = config_get_sample_cycle(); //加载用户设置
    adc_set_cycle(saved_cycle);
}

void adc_start_sampling(void) //开始定时测量
{
    g_adc_control.state = SAMPLING_ACTIVE; //设为测量状态
    g_adc_control.last_sample_time = HAL_GetTick(); //记录开始时间

    adc_process_sample(); //立即测一次
}

void adc_stop_sampling(void) //停止测量
{
    g_adc_control.state = SAMPLING_IDLE; //回到待机
    g_adc_control.over_limit = 0; //清除超限

    extern uint8_t ucLed[6]; //LED数组
    ucLed[0] = 0; //关LED1
    ucLed[1] = 0; //关LED2
}

void adc_set_cycle(sampling_cycle_t cycle) //设测量间隔
{
    g_adc_control.cycle = cycle;

    switch (cycle) {
        case CYCLE_5S:
            g_adc_control.cycle_ms = 5000; //5秒
            break;
        case CYCLE_10S:
            g_adc_control.cycle_ms = 10000; //10秒
            break;
        case CYCLE_15S:
            g_adc_control.cycle_ms = 15000; //15秒
            break;
        default:
            g_adc_control.cycle_ms = 5000; //默认5秒
            break;
    }
}

void adc_process_sample(void) //处理定时测量数据
{
    uint32_t current_time = HAL_GetTick();

    if ((current_time - g_adc_control.last_sample_time) >= g_adc_control.cycle_ms) { //时间到了
        g_adc_control.last_sample_time += g_adc_control.cycle_ms; //更新下次时间

        char time_buffer[32] = {0}; //获取当前时间
        rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

        if (g_hide_mode_enabled) { //加密模式
            hex_data_t hex_data;
            char hex_buffer[32] = {0};
            adc_convert_to_hex(&hex_data);
            adc_format_hex_string(&hex_data, hex_buffer, sizeof(hex_buffer));
            my_printf(&huart1, "%s\r\n", hex_buffer);
        } else { //正常模式
            if (g_adc_control.over_limit) {
                my_printf(&huart1, "%s ch0=%.2fV OverLimit(%.2f)!\r\n",
                         time_buffer, g_adc_control.processed_voltage, config_get_limit());
            } else {
                my_printf(&huart1, "%s ch0=%.2fV\r\n",
                         time_buffer, g_adc_control.processed_voltage);
            }
        }

        g_adc_control.display_voltage = g_adc_control.processed_voltage; //更新显示值

        if (g_adc_control.over_limit) { //保存数据
            sd_write_overlimit_data(time_buffer, g_adc_control.processed_voltage, config_get_limit());
        } else if (!g_hide_mode_enabled) {
            sd_write_sample_data(time_buffer, g_adc_control.processed_voltage);
        }

        if (g_hide_mode_enabled) { //加密数据
            sd_write_hidedata_with_voltage(g_adc_control.processed_voltage, g_adc_control.over_limit);
        }
    }
}

void adc_check_over_limit(void) //检查是否超限
{
    float limit = config_get_limit(); //用户设的限制值
    extern uint8_t ucLed[6]; //LED数组

    if (g_adc_control.processed_voltage > limit) {
        g_adc_control.over_limit = 1; //超限了
        ucLed[1] = 1; //亮LED2
    } else {
        g_adc_control.over_limit = 0; //正常
        ucLed[1] = 0; //关LED2
    }
}

void adc_convert_to_hex(hex_data_t *hex_data) //转成加密格式
{
    if (hex_data == NULL) return;

    hex_data->timestamp = rtc_get_unix_timestamp_now(); //时间戳
    hex_data->voltage_integer = (uint16_t)g_adc_control.processed_voltage; //整数部分
    float decimal_part = g_adc_control.processed_voltage - hex_data->voltage_integer;
    hex_data->voltage_decimal = (uint16_t)(decimal_part * 65536.0f); //小数部分
}

void adc_format_hex_string(const hex_data_t *hex_data, char *buffer, size_t buffer_size) //格式化16进制字符串
{
    if (hex_data == NULL || buffer == NULL || buffer_size == 0) return;

    snprintf(buffer, buffer_size, "%08lX%04X%04X", //组合成字符串
             hex_data->timestamp, //时间戳8位
             hex_data->voltage_integer, //电压整数4位
             hex_data->voltage_decimal); //电压小数4位

    if (g_adc_control.over_limit) { //超限加*号
        size_t len = strlen(buffer);
        if (len < buffer_size - 1) {
            buffer[len] = '*';
            buffer[len + 1] = '\0';
        }
    }
}

void adc_led1_blink_task(void) //LED1闪烁任务
{
    extern uint8_t ucLed[6]; //LED数组

    if (g_adc_control.state == SAMPLING_ACTIVE) {
        g_adc_control.led1_blink_state = !g_adc_control.led1_blink_state; //切换状态
        ucLed[0] = g_adc_control.led1_blink_state;
    } else {
        ucLed[0] = 0; //关闭
        g_adc_control.led1_blink_state = 0;
    }
}

