# SiMonz_HAL 核心功能模块详解

## 模块概述

SiMonz_HAL系统采用模块化设计，将系统功能划分为8个核心模块，每个模块负责特定的功能领域，通过清晰的接口进行交互。本文档详细介绍每个模块的设计思路、核心功能和技术实现。

## 1. 任务调度器模块 (scheduler)

### 设计目标
实现高效的多任务调度，确保系统各功能模块能够按照预定周期稳定运行，保证系统的实时性和可靠性。

### 核心特性
- **时间片轮询调度**: 基于系统时钟的精确时间片调度
- **任务优先级管理**: 不同任务具有不同的执行周期
- **动态任务管理**: 支持任务状态查询和统计

### 技术实现

**任务结构定义**:
```c
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;           // 任务执行周期（毫秒）
    uint32_t last_run;          // 上次执行时间戳（毫秒）
} task_t;
```

**任务配置表**:
```c
static task_t scheduler_task[] = {
    {led_task,           1,    0},     // LED控制任务
    {btn_task,           5,    0},     // 按键检测任务
    {uart_task,          5,    0},     // 串口处理任务
    {adc_task,           100,  0},     // ADC采样任务
    {adc_led1_blink_task, 1000, 0},    // LED闪烁任务
    {oled_task,          100, 0}       // OLED显示任务
};
```

**调度算法**:
- 遍历所有任务，检查是否到达执行时间
- 到达执行时间的任务立即执行
- 更新任务的上次执行时间戳
- 支持任务执行状态监控

### 关键接口
- `scheduler_init()`: 调度器初始化
- `scheduler_run()`: 调度器主循环
- `scheduler_get_task_count()`: 获取任务数量
- `scheduler_get_status()`: 获取调度器状态

## 2. 电压采集模块 (adc_app)

### 设计目标
实现高精度、高稳定性的电压采集功能，支持实时数据处理、滤波算法和超限检测。

### 核心特性
- **DMA连续采集**: 减少CPU占用，提高采集效率
- **三级滤波算法**: 中值滤波 + 滑动平均滤波
- **实时数据处理**: 变比计算、超限检测
- **灵活采样控制**: 支持启停控制和周期配置

### 技术实现

**控制结构**:
```c
typedef struct {
    sampling_state_t state;           // 采样状态
    uint32_t cycle_ms;               // 采样周期
    float processed_voltage;         // 处理后电压值
    float display_voltage;           // 显示电压值
    uint8_t over_limit;             // 超限标志
    uint32_t last_sample_time;      // 上次采样时间
    uint8_t led1_blink_state;       // LED1闪烁状态
} adc_control_t;
```

**滤波算法**:
1. **中值滤波**: 去除突发异常值
   ```c
   float voltage_median_filter(float new_value);
   ```

2. **滑动平均滤波**: 平滑数据波动
   ```c
   float voltage_sliding_average_filter(float new_value);
   ```

**数据处理流程**:
1. DMA采集原始ADC数据
2. 计算平均值并转换为电压值
3. 应用中值滤波去除异常值
4. 应用滑动平均滤波平滑数据
5. 应用用户设置的变比
6. 检测是否超过设定阈值
7. 更新显示值和状态标志

### 关键接口
- `adc_control_init()`: ADC控制初始化
- `adc_task()`: ADC采样主任务
- `adc_start_sampling()`: 开始采样
- `adc_stop_sampling()`: 停止采样
- `adc_set_cycle()`: 设置采样周期

## 3. 数据存储模块 (sd_app)

### 设计目标
实现可靠的数据存储功能，支持SD卡和Flash双重存储，确保数据安全和系统稳定性。

### 核心特性
- **双重存储机制**: SD卡主存储 + Flash备份存储
- **四类数据分离**: 正常数据、超限数据、日志、加密数据
- **自动文件管理**: 文件自动切换和状态恢复
- **错误恢复能力**: SD卡故障自动恢复

### 技术实现

**文件管理器结构**:
```c
typedef struct {
    uint16_t current_file_id;        // 当前文件ID
    uint8_t current_record_count;    // 当前文件记录数
    uint8_t is_file_open;           // 文件打开状态
    char current_filename[32];       // 当前文件名
} file_manager_t;
```

**数据类型定义**:
```c
typedef enum {
    DATA_TYPE_SAMPLE = 0,    // 正常采样数据
    DATA_TYPE_OVERLIMIT,     // 超限数据
    DATA_TYPE_LOG,           // 系统日志
    DATA_TYPE_HIDEDATA       // 加密数据
} data_type_t;
```

**存储策略**:
- 每个文件最多存储10条记录
- 文件满后自动创建新文件
- 系统重启后自动恢复到未满文件
- SD卡故障时自动切换到Flash存储

### 关键接口
- `sd_app_init()`: SD卡应用初始化
- `sd_write_sample_data()`: 写入采样数据
- `sd_write_overlimit_data()`: 写入超限数据
- `sd_write_log_data()`: 写入日志数据
- `sd_write_hidedata()`: 写入加密数据

## 4. 配置管理模块 (config_app)

### 设计目标
提供灵活的参数配置功能，支持配置的持久化存储和动态修改。

### 核心特性
- **参数持久化**: Flash存储确保配置掉电保持
- **配置同步**: SD卡配置文件备份
- **动态配置**: 运行时参数修改
- **默认值保护**: 配置加载失败时使用安全默认值

### 技术实现

**配置参数结构**:
```c
typedef struct {
    float ratio;                    // 电压变比
    float limit;                    // 超限阈值  
    sample_cycle_t sample_cycle;    // 采样周期
} config_params_t;
```

**配置操作**:
- Flash读写操作用于持久化存储
- SD卡INI文件用于配置备份和导入
- 参数范围检查确保配置有效性
- 配置变更时自动保存

### 关键接口
- `config_app_init()`: 配置管理初始化
- `config_set_ratio()`: 设置变比
- `config_set_limit()`: 设置阈值
- `config_save_to_flash()`: 保存配置到Flash
- `config_load_from_flash()`: 从Flash加载配置

## 5. 串口通信模块 (usart_app)

### 设计目标
实现丰富的串口命令系统，支持系统配置、状态查询、数据输出等功能。

### 核心特性
- **命令解析系统**: 支持多种命令格式
- **中断接收**: 基于DMA和中断的高效数据接收
- **环形缓冲区**: 防止数据丢失
- **格式化输出**: 支持printf风格的数据输出

### 技术实现

**命令类型枚举**:
```c
typedef enum {
    CMD_TEST,           // 系统测试
    CMD_RTC_CONFIG,     // RTC配置
    CMD_RTC_NOW,        // 查看时间
    CMD_CONF,           // 配置操作
    CMD_RATIO,          // 设置变比
    CMD_LIMIT,          // 设置阈值
    CMD_START,          // 开始采样
    CMD_STOP,           // 停止采样
    CMD_HIDE,           // 启用加密
    CMD_UNHIDE,         // 禁用加密
    CMD_UNKNOWN         // 未知命令
} cmd_type_t;
```

**命令处理流程**:
1. 串口中断接收数据到环形缓冲区
2. 主任务从缓冲区读取完整命令
3. 解析命令类型和参数
4. 调用对应的处理函数
5. 返回执行结果

### 关键接口
- `uart_task()`: 串口处理主任务
- `parse_uart_command()`: 命令解析
- `my_printf()`: 格式化输出
- 各种命令处理函数

## 6. 显示控制模块 (oled_app)

### 设计目标
提供直观的系统状态显示，实时显示时间、电压值和系统状态。

### 核心特性
- **实时显示**: 100ms刷新周期
- **状态切换**: 根据系统状态显示不同内容
- **格式化显示**: 时间和电压值的格式化显示
- **节能设计**: 只在数据变化时更新显示

### 技术实现
- 基于I2C接口的OLED驱动
- 状态机控制显示内容
- 字符串格式化和位置控制
- 显示缓存优化减少I2C通信

## 7. 系统自检模块 (selftest_app)

### 设计目标
提供完善的硬件自检功能，确保系统各组件正常工作。

### 核心特性
- **硬件检测**: Flash、SD卡、RTC等关键硬件检测
- **状态报告**: 详细的检测结果报告
- **设备ID管理**: 队伍编号和设备信息管理
- **启动计数**: 系统启动次数统计

### 技术实现
- 各硬件模块的功能性测试
- 设备信息的Flash存储
- 启动计数的持久化管理
- 检测结果的格式化输出

## 8. 按键控制模块 (btn_app)

### 设计目标
提供便捷的按键操作功能，支持采样控制和参数设置。

### 核心特性
- **多按键支持**: 支持多个功能按键
- **防抖处理**: 硬件和软件双重防抖
- **功能映射**: 按键功能的灵活配置
- **状态反馈**: 按键操作的即时反馈

### 技术实现
- 基于ebtn库的按键检测
- 中断触发的按键事件处理
- 按键功能的模块化设计
- 操作日志的自动记录

## 模块间交互关系

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 任务调度器   │───▶│ ADC采集     │───▶│ 数据存储    │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 串口通信    │◄───│ 配置管理    │───▶│ 系统自检    │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 显示控制    │    │ 按键控制    │    │ LED控制     │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 总结

SiMonz_HAL系统的模块化设计体现了良好的软件工程实践：

1. **高内聚低耦合**: 每个模块功能明确，接口清晰
2. **可维护性**: 模块独立开发和测试，便于维护
3. **可扩展性**: 新功能可以通过添加模块实现
4. **可重用性**: 模块可以在其他项目中重用
5. **可靠性**: 模块间的错误隔离，提高系统稳定性

这种设计使得系统具有良好的工程质量和技术价值。
