# SiMonz_HAL 技术实现要点

## 技术概述

SiMonz_HAL系统在技术实现上体现了多个嵌入式系统设计的关键技术点，本文档详细分析这些技术实现的设计思路、关键算法和工程价值。

## 1. 高精度电压采集技术

### 1.1 DMA连续采集机制

**技术原理**:
- 使用DMA方式连续采集ADC数据，减少CPU中断开销
- 采集缓冲区大小为1024个样本，提供充足的数据基础
- 12位ADC分辨率，理论精度为3.3V/4096 ≈ 0.8mV

**实现代码**:
```c
#define ADC_DMA_BUFFER_SIZE 1024
uint16_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE];

void adc_task(void) {
    uint32_t adc_sum = 0;
    
    // 计算所有采样值的平均值
    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) {
        adc_sum += adc_dma_buffer[i];
    }
    
    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE;
    float raw_voltage = ((float)adc_val * 3.3f) / 4096.0f;
    
    // 应用滤波算法
    float median_filtered = voltage_median_filter(raw_voltage);
    voltage = voltage_sliding_average_filter(median_filtered);
}
```

**技术优势**:
- 大幅减少CPU占用率
- 提供连续稳定的数据流
- 支持高频率数据采集

### 1.2 三级滤波算法

**算法设计**:
1. **中值滤波**: 去除突发异常值
2. **滑动平均滤波**: 平滑数据波动
3. **变比处理**: 适应不同测量范围

**中值滤波实现**:
```c
#define MEDIAN_FILTER_SIZE 5

float voltage_median_filter(float new_value) {
    static float filter_buffer[MEDIAN_FILTER_SIZE] = {0};
    static uint8_t filter_index = 0;
    
    // 更新滤波缓冲区
    filter_buffer[filter_index] = new_value;
    filter_index = (filter_index + 1) % MEDIAN_FILTER_SIZE;
    
    // 排序并取中值
    float sorted_buffer[MEDIAN_FILTER_SIZE];
    memcpy(sorted_buffer, filter_buffer, sizeof(filter_buffer));
    
    // 简单冒泡排序
    for (int i = 0; i < MEDIAN_FILTER_SIZE - 1; i++) {
        for (int j = 0; j < MEDIAN_FILTER_SIZE - 1 - i; j++) {
            if (sorted_buffer[j] > sorted_buffer[j + 1]) {
                float temp = sorted_buffer[j];
                sorted_buffer[j] = sorted_buffer[j + 1];
                sorted_buffer[j + 1] = temp;
            }
        }
    }
    
    return sorted_buffer[MEDIAN_FILTER_SIZE / 2];
}
```

**滑动平均滤波实现**:
```c
#define SLIDING_AVERAGE_SIZE 10

float voltage_sliding_average_filter(float new_value) {
    static float filter_buffer[SLIDING_AVERAGE_SIZE] = {0};
    static uint8_t filter_index = 0;
    static uint8_t buffer_filled = 0;
    
    filter_buffer[filter_index] = new_value;
    filter_index = (filter_index + 1) % SLIDING_AVERAGE_SIZE;
    
    if (!buffer_filled && filter_index == 0) {
        buffer_filled = 1;
    }
    
    float sum = 0;
    uint8_t count = buffer_filled ? SLIDING_AVERAGE_SIZE : filter_index;
    
    for (uint8_t i = 0; i < count; i++) {
        sum += filter_buffer[i];
    }
    
    return sum / count;
}
```

**算法效果**:
- 有效去除±0.1V以上的突发干扰
- 将测量精度提升到±0.01V
- 保持良好的动态响应特性

## 2. 双重存储可靠性设计

### 2.1 存储架构设计

**设计理念**:
- SD卡作为主存储，提供大容量数据存储
- Flash作为备份存储，确保关键数据安全
- 四类数据分离存储，便于数据管理

**存储策略**:
```c
typedef enum {
    DATA_TYPE_SAMPLE = 0,    // 正常采样数据 -> sample/
    DATA_TYPE_OVERLIMIT,     // 超限数据 -> overLimit/
    DATA_TYPE_LOG,           // 系统日志 -> log/
    DATA_TYPE_HIDEDATA       // 加密数据 -> hideData/
} data_type_t;
```

### 2.2 文件管理机制

**自动文件切换**:
```c
typedef struct {
    uint16_t current_file_id;        // 当前文件ID
    uint8_t current_record_count;    // 当前文件记录数
    uint8_t is_file_open;           // 文件打开状态
    char current_filename[32];       // 当前文件名
} file_manager_t;

#define MAX_RECORDS_PER_FILE 10

FRESULT sd_write_data(data_type_t type, const char *data) {
    file_manager_t *manager = &g_file_managers[type];
    
    // 检查是否需要创建新文件
    if (manager->current_record_count >= MAX_RECORDS_PER_FILE) {
        sd_close_current_file(type);
        manager->current_file_id++;
        manager->current_record_count = 0;
    }
    
    // 打开或创建文件
    if (!manager->is_file_open) {
        FRESULT result = sd_open_file_for_write(type);
        if (result != FR_OK) return result;
    }
    
    // 写入数据
    return sd_write_to_file(type, data);
}
```

### 2.3 错误恢复机制

**SD卡故障检测与恢复**:
```c
uint8_t sd_check_and_reinit_if_needed(void) {
    // 检查SD卡状态
    if (BSP_SD_IsDetected() != SD_PRESENT) {
        return 0; // SD卡未插入
    }
    
    // 检查文件系统状态
    FRESULT fr = f_stat("test.tmp", NULL);
    if (fr != FR_OK && fr != FR_NO_FILE) {
        // 文件系统异常，尝试重新初始化
        sd_reinitialize();
        return 1;
    }
    
    return 1; // SD卡正常
}
```

**Flash缓存机制**:
```c
FRESULT sd_write_log_data(const char *log_content) {
    // 检查SD卡状态
    if (!g_filesystem_mounted) {
        // SD卡不可用，缓存到Flash
        return flash_cache_log_data(log_content);
    }
    
    // SD卡可用，直接写入
    return sd_write_data(DATA_TYPE_LOG, formatted_data);
}
```

## 3. 实时任务调度技术

### 3.1 时间片轮询调度器

**调度算法**:
```c
void scheduler_run(void) {
    for (uint8_t i = 0; i < task_num; i++) {
        uint32_t now_time = HAL_GetTick();
        
        // 检查任务是否到达执行时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}
```

**任务优先级设计**:
- LED控制: 1ms (最高优先级，确保指示灯响应)
- 按键检测: 5ms (快速响应用户操作)
- 串口处理: 5ms (及时处理命令)
- ADC采样: 100ms (平衡精度和效率)
- OLED显示: 100ms (适中的刷新率)
- LED闪烁: 1000ms (低频状态指示)

### 3.2 中断驱动设计

**串口中断处理**:
```c
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART1) {
        uart_rx_ticks = uwTick;
        uart_rx_index++;
        HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);
    }
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size) {
    if (huart->Instance == USART1) {
        // 将接收到的数据放入环形缓冲区
        rt_ringbuffer_put(&uart_ringbuffer, uart_rx_buffer, Size);
        
        // 重新启动DMA接收
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_buffer, sizeof(uart_rx_buffer));
    }
}
```

## 4. 数据安全与加密技术

### 4.1 数据加密算法

**HEX编码加密**:
```c
void generate_hex_data(float voltage, uint8_t over_limit, char *hex_output) {
    // 基于电压值和时间戳生成伪随机HEX数据
    uint32_t timestamp = HAL_GetTick();
    uint32_t voltage_int = (uint32_t)(voltage * 1000);
    
    // 简单的异或加密
    uint32_t encrypted = voltage_int ^ timestamp ^ 0x12345678;
    
    // 转换为HEX字符串
    snprintf(hex_output, 17, "%08X%08X", 
             (unsigned int)(encrypted >> 16), 
             (unsigned int)(encrypted & 0xFFFF));
    
    // 超限标记
    if (over_limit) {
        strcat(hex_output, "*");
    }
}
```

### 4.2 数据完整性校验

**配置数据校验**:
```c
typedef struct {
    float ratio;
    float limit;
    sample_cycle_t sample_cycle;
    uint32_t checksum;  // 校验和
} config_params_with_checksum_t;

uint32_t calculate_config_checksum(config_params_t *params) {
    uint32_t checksum = 0;
    uint8_t *data = (uint8_t *)params;
    
    for (size_t i = 0; i < sizeof(config_params_t); i++) {
        checksum += data[i];
    }
    
    return checksum;
}
```

## 5. 系统自检与诊断技术

### 5.1 硬件自检算法

**Flash检测**:
```c
uint8_t selftest_flash(void) {
    uint32_t flash_id = 0;
    
    // 读取Flash ID
    if (GD25QXX_ReadID(&flash_id) == GD25QXX_OK) {
        if (flash_id == 0xEF4015) {  // 预期的Flash ID
            return 1;  // 检测成功
        }
    }
    
    return 0;  // 检测失败
}
```

**SD卡检测**:
```c
uint8_t selftest_sd_card(void) {
    // 检查SD卡物理存在
    if (BSP_SD_IsDetected() != SD_PRESENT) {
        return 0;
    }
    
    // 检查文件系统
    FRESULT fr = f_mount(&SDFatFS, SDPath, 1);
    if (fr != FR_OK) {
        return 0;
    }
    
    // 获取SD卡容量信息
    DWORD free_clusters;
    FATFS *fs;
    fr = f_getfree(SDPath, &free_clusters, &fs);
    
    return (fr == FR_OK) ? 1 : 0;
}
```

### 5.2 系统状态监控

**启动计数管理**:
```c
typedef struct {
    uint32_t team_number;      // 队伍编号
    uint32_t power_on_count;   // 上电次数
    uint8_t initialized;       // 初始化标志
} device_info_t;

void device_id_increment_boot_count(void) {
    if (!g_boot_count_incremented) {
        g_device_info.power_on_count++;
        device_id_save_to_flash();
        g_boot_count_incremented = 1;
    }
}
```

## 6. 性能优化技术

### 6.1 内存管理优化

**静态内存分配**:
- 避免动态内存分配，防止内存碎片
- 使用静态缓冲区和数组
- 合理规划内存布局

**缓冲区优化**:
```c
// 环形缓冲区用于串口数据
#define RINGBUFFER_SIZE 1024
uint8_t ringbuffer_pool[RINGBUFFER_SIZE];
rt_ringbuffer_t uart_ringbuffer;

// ADC数据缓冲区
#define ADC_DMA_BUFFER_SIZE 1024
uint16_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE];
```

### 6.2 I/O操作优化

**批量文件操作**:
```c
FRESULT sd_batch_write_data(data_type_t type, const char *data[], uint8_t count) {
    // 一次性写入多条数据，减少文件系统开销
    for (uint8_t i = 0; i < count; i++) {
        FRESULT result = sd_write_data(type, data[i]);
        if (result != FR_OK) return result;
    }
    
    // 强制同步到存储介质
    return f_sync(&g_file_handles[type]);
}
```

## 7. 错误处理与容错技术

### 7.1 分层错误处理

**硬件层错误处理**:
- HAL库返回值检查
- 硬件状态监控
- 自动重试机制

**应用层错误处理**:
- 参数有效性检查
- 状态一致性验证
- 优雅降级处理

### 7.2 系统恢复机制

**状态恢复**:
```c
void system_recovery_procedure(void) {
    // 1. 检查系统关键状态
    if (!scheduler_get_status()) {
        scheduler_init();
    }
    
    // 2. 恢复存储系统
    if (!g_filesystem_mounted) {
        sd_reinitialize();
    }
    
    // 3. 恢复配置参数
    if (config_load_from_flash() != CONFIG_OK) {
        config_load_defaults();
    }
    
    // 4. 重置错误标志
    system_clear_error_flags();
}
```

## 技术价值总结

SiMonz_HAL系统的技术实现体现了以下工程价值：

1. **高可靠性**: 多重错误检测和恢复机制
2. **高精度**: 先进的滤波算法和校准技术
3. **高效率**: 优化的任务调度和内存管理
4. **高安全性**: 数据加密和完整性校验
5. **高可维护性**: 模块化设计和清晰的接口

这些技术实现为嵌入式系统开发提供了宝贵的参考价值。

## 8. 命令系统与用户交互

### 8.1 命令解析系统

**命令类型定义**:
```c
typedef enum {
    CMD_TEST,           // 系统测试命令
    CMD_RTC_CONFIG,     // RTC时间配置
    CMD_RTC_NOW,        // 查看当前时间
    CMD_CONF,           // 配置文件操作
    CMD_RATIO,          // 设置电压变比
    CMD_LIMIT,          // 设置超限阈值
    CMD_START,          // 开始采样
    CMD_STOP,           // 停止采样
    CMD_HIDE,           // 启用数据加密
    CMD_UNHIDE,         // 禁用数据加密
    CMD_CONFIG_SAVE,    // 保存配置
    CMD_CONFIG_READ,    // 读取配置
    CMD_UNKNOWN         // 未知命令
} cmd_type_t;
```

**命令解析算法**:
```c
cmd_type_t parse_command_type(char *cmd_str) {
    if (strncmp(cmd_str, "test", 4) == 0) return CMD_TEST;
    if (strncmp(cmd_str, "RTC Config", 10) == 0) return CMD_RTC_CONFIG;
    if (strncmp(cmd_str, "RTC now", 7) == 0) return CMD_RTC_NOW;
    if (strncmp(cmd_str, "conf", 4) == 0) return CMD_CONF;
    if (strncmp(cmd_str, "ratio", 5) == 0) return CMD_RATIO;
    if (strncmp(cmd_str, "limit", 5) == 0) return CMD_LIMIT;
    if (strncmp(cmd_str, "start", 5) == 0) return CMD_START;
    if (strncmp(cmd_str, "stop", 4) == 0) return CMD_STOP;
    if (strncmp(cmd_str, "hide", 4) == 0) return CMD_HIDE;
    if (strncmp(cmd_str, "unhide", 6) == 0) return CMD_UNHIDE;
    if (strncmp(cmd_str, "config save", 11) == 0) return CMD_CONFIG_SAVE;
    if (strncmp(cmd_str, "config read", 11) == 0) return CMD_CONFIG_READ;

    return CMD_UNKNOWN;
}
```

### 8.2 参数验证与处理

**变比参数处理**:
```c
void handle_ratio_cmd(char *params) {
    float ratio_value = atof(params);

    // 参数范围检查
    if (ratio_value < 0.1f || ratio_value > 100.0f) {
        my_printf(&huart1, "Error: ratio must be between 0.1 and 100.0\r\n");
        return;
    }

    // 设置变比并保存
    config_set_ratio(ratio_value);
    config_save_to_flash();

    my_printf(&huart1, "ratio:%.1f\r\n", ratio_value);

    // 记录操作日志
    char log_buffer[64];
    snprintf(log_buffer, sizeof(log_buffer), "ratio set to %.1f", ratio_value);
    sd_write_log_data(log_buffer);
}
```

**时间配置处理**:
```c
void handle_rtc_config_cmd(char *params) {
    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};

    // 解析时间字符串 "YYYY-MM-DD HH:MM:SS"
    int year, month, day, hour, minute, second;
    int parsed = sscanf(params, "%d-%d-%d %d:%d:%d",
                       &year, &month, &day, &hour, &minute, &second);

    if (parsed != 6) {
        my_printf(&huart1, "Error: Invalid time format\r\n");
        return;
    }

    // 参数有效性检查
    if (year < 2000 || year > 2099 || month < 1 || month > 12 ||
        day < 1 || day > 31 || hour < 0 || hour > 23 ||
        minute < 0 || minute > 59 || second < 0 || second > 59) {
        my_printf(&huart1, "Error: Invalid time values\r\n");
        return;
    }

    // 设置RTC时间
    rtc_set_time(year, month, day, hour, minute, second);
    my_printf(&huart1, "rtc config success to %04d-%02d-%02d %02d:%02d:%02d\r\n",
              year, month, day, hour, minute, second);
}
```

## 9. 数据格式化与输出

### 9.1 数据格式标准化

**采样数据格式**:
```c
FRESULT format_sample_data(float voltage, char *output) {
    char time_str[32];

    // 获取当前时间
    if (rtc_get_time_string(time_str, sizeof(time_str)) != RTC_OK) {
        return FR_INT_ERR;
    }

    // 格式化数据: "2025-01-15 10:30:25 ch0=2.45V"
    snprintf(output, 128, "%s ch0=%.2fV", time_str, voltage);

    return FR_OK;
}
```

**超限数据格式**:
```c
FRESULT format_overlimit_data(float voltage, float limit, char *output) {
    char time_str[32];

    if (rtc_get_time_string(time_str, sizeof(time_str)) != RTC_OK) {
        return FR_INT_ERR;
    }

    // 格式化超限数据: "2025-01-15 10:30:25 ch0=2.55V OverLimit(2.50)!"
    snprintf(output, 128, "%s ch0=%.2fV OverLimit(%.2f)!",
             time_str, voltage, limit);

    return FR_OK;
}
```

### 9.2 加密数据生成

**HEX数据生成算法**:
```c
void sd_write_hidedata_with_voltage(float voltage, uint8_t over_limit) {
    char hex_data[32];

    // 基于电压值生成伪随机HEX数据
    uint32_t voltage_scaled = (uint32_t)(voltage * 1000);
    uint32_t timestamp = HAL_GetTick();

    // 使用简单的线性同余生成器
    static uint32_t seed = 0x12345678;
    seed = (seed * 1103515245 + 12345) & 0x7FFFFFFF;

    uint32_t data1 = (voltage_scaled ^ timestamp ^ seed) & 0xFFFFFFFF;
    uint32_t data2 = ((voltage_scaled << 8) ^ (timestamp >> 8) ^ (seed >> 16)) & 0xFFFFFFFF;

    // 生成16位HEX字符串
    snprintf(hex_data, sizeof(hex_data), "%08X%08X",
             (unsigned int)data1, (unsigned int)data2);

    // 添加超限标记
    if (over_limit) {
        strcat(hex_data, "*");
    }

    // 写入加密数据文件
    sd_write_data(DATA_TYPE_HIDEDATA, hex_data);
}
```

这些技术实现展示了系统在用户交互、数据处理和安全性方面的完善设计。
