# SiMonz_HAL 嵌入式电压采集系统 - 系统架构总览

## 项目基本信息

- **项目名称**: SiMonz_HAL 嵌入式电压采集系统
- **队伍编号**: 2025478430
- **开发环境**: Keil IDE V5
- **目标平台**: STM32F407VGT6
- **项目性质**: 2025年西门子杯竞赛项目

## 系统概述

SiMonz_HAL是一个基于STM32F407的高精度电压采集系统，专为嵌入式竞赛设计。系统实现了实时电压监测、数据存储、超限报警、数据加密等核心功能，具备高可靠性、高精度、强实时性的特点。

### 核心功能特性

1. **高精度电压采集**
   - 测量范围：0-3.3V
   - 测量精度：±0.01V
   - 三级滤波算法确保数据质量

2. **实时数据处理**
   - 可配置采样周期（5s/10s/15s）
   - 实时超限检测和报警
   - 支持变比设置和阈值配置

3. **双重数据存储**
   - SD卡主存储 + Flash备份存储
   - 四类数据分离管理
   - 断电保护和自动恢复

4. **多样化用户交互**
   - OLED实时显示
   - LED状态指示
   - 串口命令控制
   - 按键快捷操作

5. **数据安全保障**
   - 支持数据加密传输
   - 完整性校验机制
   - 错误检测和恢复

## 系统架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    SiMonz_HAL 系统架构                        │
├─────────────────────────────────────────────────────────────┤
│  用户交互层                                                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ OLED显示屏   │ │ LED指示灯   │ │ 按键输入    │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  应用层 (sysFunction)                                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ 任务调度器   │ │ 配置管理    │ │ 系统自检    │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ ADC采集     │ │ 数据存储    │ │ 串口通信    │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  硬件抽象层 (HAL)                                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ ADC驱动     │ │ SDIO驱动    │ │ UART驱动    │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ SPI驱动     │ │ I2C驱动     │ │ GPIO驱动    │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  硬件层                                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ STM32F407   │ │ SD卡        │ │ Flash存储   │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ OLED屏幕    │ │ 按键        │ │ LED灯       │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
```

### 核心模块架构

#### 1. 任务调度器 (scheduler)
- **设计模式**: 时间片轮询调度
- **核心特性**: 
  - 支持多任务并发执行
  - 每个任务独立的执行周期
  - 高效的任务管理和调度

**任务列表**:
```c
static task_t scheduler_task[] = {
    {led_task,           1,    0},     // LED控制 - 1ms周期
    {btn_task,           5,    0},     // 按键检测 - 5ms周期  
    {uart_task,          5,    0},     // 串口处理 - 5ms周期
    {adc_task,           100,  0},     // ADC采样 - 100ms周期
    {adc_led1_blink_task, 1000, 0},    // LED闪烁 - 1000ms周期
    {oled_task,          100, 0}       // OLED显示 - 100ms周期
};
```

#### 2. 电压采集模块 (adc_app)
- **采集方式**: DMA连续采集
- **滤波算法**: 中值滤波 + 滑动平均滤波
- **数据处理**: 实时电压计算、变比处理、超限检测

**核心数据结构**:
```c
typedef struct {
    sampling_state_t state;           // 采样状态
    uint32_t cycle_ms;               // 采样周期
    float processed_voltage;         // 处理后电压值
    float display_voltage;           // 显示电压值
    uint8_t over_limit;             // 超限标志
    uint32_t last_sample_time;      // 上次采样时间
    uint8_t led1_blink_state;       // LED1闪烁状态
} adc_control_t;
```

#### 3. 数据存储模块 (sd_app)
- **存储策略**: SD卡主存储 + Flash备份
- **数据分类**: 四类数据分离存储
- **文件管理**: 自动文件切换和状态恢复

**存储目录结构**:
```
SD卡根目录/
├── sample/          # 正常采样数据
├── overLimit/       # 超限数据  
├── log/            # 系统日志
├── hideData/       # 加密数据
└── config.ini      # 配置文件
```

#### 4. 配置管理模块 (config_app)
- **参数管理**: 变比、阈值、采样周期等
- **存储方式**: Flash持久化存储
- **配置同步**: SD卡配置文件同步

**配置参数结构**:
```c
typedef struct {
    float ratio;                    // 电压变比
    float limit;                    // 超限阈值
    sample_cycle_t sample_cycle;    // 采样周期
} config_params_t;
```

## 技术特色与创新点

### 1. 高可靠性设计
- **双重存储机制**: SD卡故障时自动切换到Flash存储
- **错误恢复能力**: 自动检测和修复存储错误
- **状态一致性**: 完善的状态检查和同步机制
- **断电保护**: 系统重启后自动恢复工作状态

### 2. 高精度测量算法
- **多级滤波**: 中值滤波去除异常值，滑动平均滤波平滑数据
- **DMA采集**: 减少CPU占用，提高采集效率
- **精确校准**: 支持变比设置，适应不同测量范围
- **实时处理**: 100ms周期的实时数据处理

### 3. 灵活的配置系统
- **参数可调**: 支持变比、阈值、采样周期等参数配置
- **多种操作方式**: 串口命令和按键操作双重支持
- **配置持久化**: Flash存储确保配置掉电保持
- **配置同步**: SD卡配置文件实现配置备份

### 4. 实时响应能力
- **任务调度器**: 时间片轮询确保实时性
- **中断驱动**: 串口、按键等采用中断方式处理
- **非阻塞设计**: 避免长时间阻塞操作影响系统响应

### 5. 数据安全保障
- **加密传输**: 支持HEX格式数据加密输出
- **分类存储**: 不同类型数据分离管理
- **完整性校验**: 数据写入和读取的完整性验证
- **访问控制**: 严格的文件访问权限管理

## 系统性能指标

| 性能指标 | 规格参数 | 备注 |
|---------|---------|------|
| 电压测量范围 | 0-3.3V | ADC满量程 |
| 测量精度 | ±0.01V | 三级滤波后精度 |
| 采样周期 | 5s/10s/15s | 可配置选择 |
| 响应时间 | <100ms | 任务调度周期 |
| 存储容量 | SD卡容量 | 支持大容量存储 |
| 数据备份 | Flash 2MB | 关键数据备份 |
| 通信速率 | 115200 bps | 串口通信速率 |
| 显示刷新 | 10Hz | OLED显示刷新率 |

## 开发环境与工具链

- **开发环境**: Keil MDK-ARM V5
- **编译器**: ARM Compiler V6
- **调试器**: J-Link
- **版本控制**: Git
- **文档工具**: Markdown
- **测试工具**: 串口调试助手

## 项目文件结构

```
SiMonz_HAL/
├── Components/          # 外设组件驱动
│   ├── GD25QXX/        # Flash驱动
│   ├── ebtn/           # 按键驱动  
│   ├── oled/           # OLED驱动
│   └── ringbuffer/     # 环形缓冲区
├── Core/               # STM32 HAL核心文件
│   ├── Inc/           # 头文件
│   └── Src/           # 源文件
├── Drivers/            # STM32 HAL驱动库
├── FATFS/             # 文件系统
├── Middlewares/        # 中间件
├── sysFunction/        # 系统功能模块
│   ├── adc_app.*      # ADC应用层
│   ├── btn_app.*      # 按键应用层
│   ├── config_app.*   # 配置管理
│   ├── flash_app.*    # Flash应用层
│   ├── led_app.*      # LED应用层
│   ├── oled_app.*     # OLED应用层
│   ├── rtc_app.*      # RTC应用层
│   ├── scheduler.*    # 任务调度器
│   ├── sd_app.*       # SD卡应用层
│   ├── selftest_app.* # 系统自检
│   └── usart_app.*    # 串口应用层
├── MDK-ARM/           # Keil工程文件
└── docs/              # 项目文档
```

## 总结

SiMonz_HAL嵌入式电压采集系统是一个设计完善、功能丰富的嵌入式解决方案。系统采用模块化设计，具有高可靠性、高精度、强实时性的特点，体现了嵌入式系统设计的最佳实践。该项目不仅适用于竞赛场景，也具有很强的工程实用价值和教学参考意义。
