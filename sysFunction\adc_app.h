#ifndef __ADC_APP_H_
#define __ADC_APP_H_
#define ADC_DMA_BUFFER_SIZE 32 // DMA缓冲区大小，可以根据需要调整
#include "stdint.h"
#include "mydefine.h"
#include "sampling_types.h"  // 包含采样相关类型定义

// 数据采集控制结构体
typedef struct {
    sampling_state_t state;         // 采样状态
    sampling_cycle_t cycle;         // 采样周期
    uint32_t last_sample_time;      // 上次采样时间
    uint32_t cycle_ms;              // 当前周期毫秒数
    float processed_voltage;        // 处理后的电压值（应用变比）
    float display_voltage;          // OLED显示用的电压值（只在串口输出时更新）
    uint8_t over_limit;             // 超限标志
    uint8_t led1_blink_state;       // LED1闪烁状态
} adc_control_t;

// HEX数据结构体
typedef struct {
    uint32_t timestamp;             // Unix时间戳
    uint16_t voltage_integer;       // 电压整数部分
    uint16_t voltage_decimal;       // 电压小数部分
} hex_data_t;

// 全局控制变量
extern adc_control_t g_adc_control;
extern uint8_t g_hide_mode_enabled;  // 加密存储模式标志

// 原有函数声明
void adc_task(void);
void dac_sin_init(void);
void adc_dma_init(void);
void adc_tim_dma_init(void);

// 新增函数声明
void adc_control_init(void);                                    // 采样控制初始化
void adc_start_sampling(void);                                  // 开始采样
void adc_stop_sampling(void);                                   // 停止采样
void adc_set_cycle(sampling_cycle_t cycle);                     // 设置采样周期
void adc_process_sample(void);                                  // 处理采样数据
void adc_check_over_limit(void);                                // 检查超限
void adc_convert_to_hex(hex_data_t *hex_data);                  // 转换为HEX格式
void adc_format_hex_string(const hex_data_t *hex_data, char *buffer, size_t buffer_size); // 格式化HEX字符串
void adc_led1_blink_task(void);                                 // LED1闪烁任务

extern uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; // DMA 目标缓冲区
extern __IO uint32_t adc_val;                        // 用于存储计算后的平均 ADC 值
extern __IO float voltage;                           // 用于存储计算后的电压值
#endif /* __ADC_APP_H_ */
