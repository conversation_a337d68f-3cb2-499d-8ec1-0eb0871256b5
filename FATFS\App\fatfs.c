/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file   fatfs.c
  * @brief  Code for fatfs applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
#include "fatfs.h"
#include "rtc.h"  // 添加RTC头文件以使用hrtc和RTC相关类型

uint8_t retSD;    /* Return value for SD */
char SDPath[4];   /* SD logical drive path */
FATFS SDFatFS;    /* File system object for SD logical drive */
FIL SDFile;       /* File object for SD */

/* USER CODE BEGIN Variables */

/* USER CODE END Variables */

void MX_FATFS_Init(void)
{
  /*## FatFS: Link the SD driver ###########################*/
  retSD = FATFS_LinkDriver(&SD_Driver, SDPath);

  /* USER CODE BEGIN Init */
  /* additional user code for init */
  /* USER CODE END Init */
}

/**
  * @brief  Gets Time from RTC
  * @param  None
  * @retval Time in DWORD
  */
DWORD get_fattime(void)
{
  /* USER CODE BEGIN get_fattime */
  RTC_TimeTypeDef current_time = {0};
  RTC_DateTypeDef current_date = {0};

  // 获取当前RTC时间
  HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);

  // FATFS时间格式：
  // bit31:25 年份（从1980年开始计算）
  // bit24:21 月份（1-12）
  // bit20:16 日期（1-31）
  // bit15:11 小时（0-23）
  // bit10:5  分钟（0-59）
  // bit4:0   秒/2（0-29）

  DWORD fattime = 0;

  // 年份（2000+Year-1980）
  fattime |= ((DWORD)(current_date.Year + 2000 - 1980) & 0x7F) << 25;

  // 月份
  fattime |= ((DWORD)current_date.Month & 0x0F) << 21;

  // 日期
  fattime |= ((DWORD)current_date.Date & 0x1F) << 16;

  // 小时
  fattime |= ((DWORD)current_time.Hours & 0x1F) << 11;

  // 分钟
  fattime |= ((DWORD)current_time.Minutes & 0x3F) << 5;

  // 秒（除以2）
  fattime |= ((DWORD)current_time.Seconds / 2) & 0x1F;

  return fattime;
  /* USER CODE END get_fattime */
}

/* USER CODE BEGIN Application */

/* USER CODE END Application */
