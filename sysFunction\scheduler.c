#include "scheduler.h"
#include "selftest_app.h"
#include "config_app.h"
#include "sd_app.h"

// --- 调度器全局变量定义区域 ---

/**
 * @brief 当前系统中的任务总数
 * @note  在scheduler_init()中自动计算，用于任务遍历的边界检查
 */
uint8_t task_num;


typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;           // 任务执行周期（毫秒）
    uint32_t last_run;          // 上次执行时间戳（毫秒）
} task_t;


static task_t scheduler_task[] =
{
    {led_task,           1,    0},
    {btn_task,           5,    0},
    {uart_task,          5,    0},
    {adc_task,           100,  0},
    {adc_led1_blink_task, 1000, 0},
    {oled_task,          100, 0}
};


void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);

    flash_app_init();
    config_app_init();
    adc_control_init();
    device_id_init();

    // 从Flash加载设备ID（确保队伍ID从Flash读取）
    device_id_load_from_flash();

    HAL_Delay(10);

    system_startup_sequence();

    sd_app_init();

    // 初始化测试阶段管理器（必须在SD卡初始化后执行）
    test_stage_init();

    config_ensure_ini_file();
}


void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = HAL_GetTick();

        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}


uint8_t scheduler_get_task_count(void)
{
    return task_num;
}

uint8_t scheduler_get_status(void)
{
    return (task_num > 0 && task_num < 20) ? 1 : 0;
}


