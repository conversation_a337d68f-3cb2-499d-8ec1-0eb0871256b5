#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "mydefine.h"

// 指令类型枚举
typedef enum {
    CMD_NONE = 0,
    CMD_TEST,           // test指令
    CMD_TEST_SIMPLE,    // test simple指令（简化Flash检测）
    CMD_SD_TEST,        // sd test指令（SD卡测试）
    CMD_SD_DETECT,      // sd detect指令（SD卡重新检测）
    CMD_SD_FORMAT,      // sd format指令（SD卡格式化）
    CMD_RTC_CONFIG,     // RTC Config指令
    CMD_RTC_NOW,        // RTC now指令
    CMD_CONF,           // conf指令
    CMD_CONF_TEST,      // conf test指令（SD卡文件操作测试）
    CMD_CONF_DELETE,    // conf delete指令（删除config.ini文件，测试用）
    CMD_CONF_FIX,       // conf fix指令（修复SD卡文件系统问题）
    CMD_RATIO,          // ratio指令
    CMD_LIMIT,          // limit指令
    CMD_CONFIG_SAVE,    // config save指令
    CMD_CONFIG_READ,    // config read指令
    CMD_START,          // start指令
    CMD_STOP,           // stop指令
    CMD_HIDE,           // hide指令
    CMD_UNHIDE,         // unhide指令
    CMD_SYSTEM_STATUS,  // system status指令
    CMD_RESET_BOOT,     // reset boot指令（重置启动次数，调试用）
    CMD_RESET_STAGE,    // reset stage指令（重置测试阶段状态）
    CMD_RECOVER_SD,     // recover sd指令（SD卡异常恢复）
    CMD_CHECK_STATE,    // check state指令（检查状态一致性）
    CMD_LOG_VALIDATE,   // log validate指令（验证日志文件）
    CMD_TEST_SIMULATE,  // test simulate指令（模拟测试序列）
    CMD_TEST_REPORT,    // test report指令（生成测试报告）
    CMD_DEBUG_STATE,    // debug state指令（详细系统状态）
    CMD_SD_SYNC,        // sd sync指令（强制同步文件系统）
    CMD_MAX
} cmd_type_t;

// 指令解析状态枚举
typedef enum {
    CMD_STATE_IDLE = 0,     // 空闲状态
    CMD_STATE_PARSING,      // 解析状态
    CMD_STATE_WAITING,      // 等待参数输入状态
    CMD_STATE_PROCESSING    // 处理状态
} cmd_state_t;

// 指令处理函数指针类型
typedef void (*cmd_handler_t)(char *params);

// 指令结构体
typedef struct {
    const char *cmd_str;    // 指令字符串
    cmd_type_t cmd_type;    // 指令类型
    cmd_handler_t handler;  // 处理函数
} cmd_entry_t;

// 函数声明
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
void uart_task(void);
void parse_uart_command(uint8_t *buffer, uint16_t length);
cmd_type_t parse_command_type(char *cmd_str);
void handle_test_cmd(char *params);
void handle_test_simple_cmd(char *params);
void handle_sd_test_cmd(char *params);
void handle_sd_detect_cmd(char *params);
void handle_sd_format_cmd(char *params);
void handle_rtc_config_cmd(char *params);
void handle_rtc_now_cmd(char *params);
void handle_conf_cmd(char *params);
void handle_conf_test_cmd(char *params);
void handle_conf_delete_cmd(char *params);
void handle_conf_fix_cmd(char *params);
void handle_ratio_cmd(char *params);
void handle_limit_cmd(char *params);
void handle_config_save_cmd(char *params);
void handle_config_read_cmd(char *params);
void handle_start_cmd(char *params);
void handle_stop_cmd(char *params);
void handle_hide_cmd(char *params);
void handle_unhide_cmd(char *params);
void handle_system_status_cmd(char *params);
void handle_reset_boot_cmd(char *params);
void handle_reset_stage_cmd(char *params);
void handle_recover_sd_cmd(char *params);
void handle_check_state_cmd(char *params);
void handle_log_validate_cmd(char *params);
void handle_test_simulate_cmd(char *params);
void handle_test_report_cmd(char *params);
void handle_debug_state_cmd(char *params);
void handle_sd_sync_cmd(char *params);

#endif


