# SiMonz_HAL 使用指南和命令参考

## 快速开始指南

### 系统启动流程

1. **硬件连接**
   - 连接5V电源适配器
   - 插入格式化的SD卡（FAT32格式）
   - 连接串口调试线（波特率115200）
   - 连接待测电压信号到ADC输入端

2. **系统上电**
   - 接通电源，系统自动启动
   - OLED显示"system idle"表示系统就绪
   - 串口输出启动信息

3. **基本验证**
   ```
   发送命令: test
   预期输出: 系统自检结果
   ```

### 基本操作流程

1. **设置系统时间**
   ```
   RTC Config 2025-01-15 10:30:00
   ```

2. **配置测量参数**
   ```
   ratio 2.5      # 设置变比为2.5
   limit 5.0      # 设置阈值为5.0V
   config save    # 保存配置
   ```

3. **开始测量**
   ```
   start          # 开始采样
   ```
   或按下KEY1按键

4. **查看数据**
   - OLED屏幕实时显示电压值
   - 串口输出采样数据
   - SD卡自动保存数据文件

## 完整命令参考

### 1. 系统测试命令

#### test
**功能**: 执行完整的系统硬件自检
**格式**: `test`
**示例**:
```
> test
======system selftest======
flash.........ok
TF card..........ok
flash ID: 0xEF4015
TF card memory: 7580672 KB
RTC: 2025-01-15 10:30:25
======system selftest======
```

#### test simple
**功能**: 执行简化的系统自检
**格式**: `test simple`
**示例**:
```
> test simple
system hardware test
test ok
```

### 2. 时间管理命令

#### RTC Config
**功能**: 设置系统时间
**格式**: `RTC Config YYYY-MM-DD HH:MM:SS`
**参数**: 
- YYYY: 年份 (2000-2099)
- MM: 月份 (01-12)
- DD: 日期 (01-31)
- HH: 小时 (00-23)
- MM: 分钟 (00-59)
- SS: 秒钟 (00-59)

**示例**:
```
> RTC Config 2025-01-15 10:30:00
rtc config success to 2025-01-15 10:30:00
```

#### RTC now
**功能**: 查看当前系统时间
**格式**: `RTC now`
**示例**:
```
> RTC now
rtc now - current time: 2025-01-15 10:30:25
```

### 3. 配置管理命令

#### ratio
**功能**: 设置电压变比
**格式**: `ratio <值>`
**参数**: 浮点数，范围 0.1-100.0
**示例**:
```
> ratio 2.5
ratio:2.5
```

#### limit
**功能**: 设置超限阈值
**格式**: `limit <值>`
**参数**: 浮点数，范围 0.1-200.0
**示例**:
```
> limit 5.0
limit:5.00
```

#### config save
**功能**: 保存当前配置到Flash和SD卡
**格式**: `config save`
**示例**:
```
> config save
ratio:2.5
limit:5.00
config save success
```

#### config read
**功能**: 从Flash读取配置参数
**格式**: `config read`
**示例**:
```
> config read
read parameters from flash
ratio:2.5
limit:5.00
```

#### conf
**功能**: 从SD卡config.ini文件读取配置
**格式**: `conf`
**示例**:
```
> conf
Ratio = 2.5, Limit = 5.00, config read success
```

### 4. 采样控制命令

#### start
**功能**: 开始周期性电压采样
**格式**: `start`
**示例**:
```
> start
Periodic Sampling
sample cycle: 5s
```

#### stop
**功能**: 停止周期性电压采样
**格式**: `stop`
**示例**:
```
> stop
Periodic Sampling STOP
```

### 5. 数据加密命令

#### hide
**功能**: 启用数据加密模式
**格式**: `hide`
**示例**:
```
> hide
data encryption enabled
```
**说明**: 启用后，采样数据将以HEX格式输出

#### unhide
**功能**: 禁用数据加密模式
**格式**: `unhide`
**示例**:
```
> unhide
data encryption disabled
```

### 6. SD卡管理命令

#### sd detect
**功能**: 检测SD卡状态
**格式**: `sd detect`
**示例**:
```
> sd detect
SD card detected and mounted successfully
```

#### sd format
**功能**: 格式化SD卡（谨慎使用）
**格式**: `sd format`
**警告**: 此操作会删除SD卡所有数据

#### sd sync
**功能**: 强制同步文件系统
**格式**: `sd sync`
**示例**:
```
> sd sync
File system synchronized
```

### 7. 系统维护命令

#### system status
**功能**: 显示系统运行状态
**格式**: `system status`
**示例**:
```
> system status
System Status:
- Scheduler: Running (6 tasks)
- ADC: Active, Voltage: 2.45V
- Storage: SD Card OK, Flash OK
- Config: Ratio=2.5, Limit=5.0V
```

#### debug state
**功能**: 输出详细的系统状态信息
**格式**: `debug state`
**用途**: 故障诊断和系统调试

#### reset boot
**功能**: 重置系统启动次数计数器
**格式**: `reset boot`
**示例**:
```
> reset boot
Boot count reset to 0
```

#### reset stage
**功能**: 重置测试阶段状态
**格式**: `reset stage`

## 按键操作参考

### KEY1 - 采样控制键
**功能**: 启动/停止采样切换
**操作**: 短按KEY1
**效果**: 
- 系统空闲时：开始采样，LED1开始闪烁
- 正在采样时：停止采样，LED1熄灭

### KEY2 - 5秒周期设置
**功能**: 设置采样周期为5秒
**操作**: 短按KEY2
**效果**: 采样周期切换到5秒，串口输出确认信息

### KEY3 - 10秒周期设置
**功能**: 设置采样周期为10秒
**操作**: 短按KEY3
**效果**: 采样周期切换到10秒，串口输出确认信息

### KEY4 - 15秒周期设置
**功能**: 设置采样周期为15秒
**操作**: 短按KEY4
**效果**: 采样周期切换到15秒，串口输出确认信息

## 显示界面说明

### OLED显示模式

#### 空闲状态显示
```
    system idle
```

#### 采样状态显示
```
   10:30:25
   
    2.45V
```
- 第一行：当前时间
- 第二行：实时电压值

### LED指示说明

#### LED1 - 采样状态指示
- **熄灭**: 系统空闲，未进行采样
- **1秒闪烁**: 正在进行周期性采样

#### LED2 - 超限报警指示
- **熄灭**: 电压值正常，未超过设定阈值
- **常亮**: 电压值超过设定阈值，触发报警

## 数据文件说明

### 文件目录结构
```
SD卡根目录/
├── sample/          # 正常采样数据
│   ├── sample0.txt
│   ├── sample1.txt
│   └── ...
├── overLimit/       # 超限数据
│   ├── overLimit0.txt
│   ├── overLimit1.txt
│   └── ...
├── log/            # 系统日志
│   ├── log0.txt
│   ├── log1.txt
│   └── ...
├── hideData/       # 加密数据
│   ├── hideData0.txt
│   ├── hideData1.txt
│   └── ...
└── config.ini      # 配置文件
```

### 数据格式说明

#### 正常采样数据 (sample/*.txt)
```
2025-01-15 10:30:25 ch0=2.45V
2025-01-15 10:30:30 ch0=2.48V
2025-01-15 10:30:35 ch0=2.50V
```

#### 超限数据 (overLimit/*.txt)
```
2025-01-15 10:30:40 ch0=2.55V OverLimit(2.50)!
2025-01-15 10:30:45 ch0=2.58V OverLimit(2.50)!
```

#### 系统日志 (log/*.txt)
```
system init
rtc config success to 2025-01-15 10:30:00
sample start - cycle 5s (command)
sample stop (command)
```

#### 加密数据 (hideData/*.txt)
```
67890ABC12345678
67890DEF12345679*
```
*注：末尾的*号表示该数据超限*

#### 配置文件 (config.ini)
```
[Ratio]
Ch0 = 2.5

[Limit]
Ch0 = 5.00
```

## 常见问题解决

### 1. 系统无法启动
**症状**: 上电后无任何显示
**解决方法**:
1. 检查电源连接是否正常
2. 确认电源适配器输出5V
3. 检查硬件连接

### 2. SD卡无法识别
**症状**: 串口提示SD卡错误
**解决方法**:
1. 确保SD卡格式为FAT32
2. 重新插拔SD卡
3. 使用`sd detect`命令检测
4. 必要时使用`sd format`格式化

### 3. 时间显示错误
**症状**: OLED显示时间不正确
**解决方法**:
1. 使用`RTC Config`命令重新设置时间
2. 检查RTC电池电量
3. 使用`test`命令检查RTC状态

### 4. 电压测量不准确
**症状**: 显示电压与实际电压差异较大
**解决方法**:
1. 检查输入电压是否在0-3.3V范围内
2. 调整变比参数：`ratio <新值>`
3. 确认ADC输入连接正确

### 5. 数据丢失
**症状**: SD卡中找不到数据文件
**解决方法**:
1. 检查SD卡是否正常工作
2. 系统会自动从Flash恢复重要数据
3. 使用`sd sync`强制同步文件系统

## 技术支持

如遇到其他问题，请：
1. 使用`test`命令进行系统自检
2. 使用`debug state`命令获取详细状态信息
3. 检查串口输出的错误信息
4. 联系技术支持团队

---
**注意事项**:
1. 系统运行时请勿随意断电
2. 定期备份重要数据
3. 输入电压不要超过3.3V
4. 操作前请仔细阅读本手册
