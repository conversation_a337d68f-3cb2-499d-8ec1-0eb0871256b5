#include "btn_app.h"
#include "ebtn.h"
#include "gpio.h"
#include "adc_app.h"
#include "usart_app.h"
#include "config_app.h"
#include "rtc_app.h"
#include "sd_app.h"

/**
 * @brief LED控制数组的外部引用
 * @note  用于按键控制LED状态
 */
extern uint8_t ucLed[6];

typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_3,
    USER_BUTTON_4,
    USER_BUTTON_5,
    USER_BUTTON_MAX,

} user_button_t;

/**
 * @brief 默认按键参数配置
 * @details 配置按键的防抖时间、有效按压时间等参数
 *          参数说明：
 *          - 防抖时间：20ms（按下）
 *          - 释放防抖：0ms（释放）
 *          - 最小按压时间：20ms（有效点击）
 *          - 最大按压时间：1000ms（长按检测）
 *          - 连击间隔：0ms（不检测连击）
 *          - 保持事件周期：1000ms（长按周期）
 *          - 最大连击次数：10次
 */
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

/**
 * @brief 按键对象数组
 * @details 为每个物理按键创建对应的按键对象
 *          使用统一的参数配置，确保按键行为一致
 */
static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),    // 初始化KEY0
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),    // 初始化KEY1
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),    // 初始化KEY2
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),    // 初始化KEY3
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),    // 初始化KEY4
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),    // 初始化KEY5
};


uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id)
    {
    case USER_BUTTON_0:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
    case USER_BUTTON_1:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13);
    case USER_BUTTON_2:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11);
    case USER_BUTTON_3:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9);
    case USER_BUTTON_4:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7);
    case USER_BUTTON_5:
        return !HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);
    default:
        return 0;
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    // KEY1 (USER_BUTTON_1): 采样启停控制
    if ((btn->key_id == USER_BUTTON_1) && (ebtn_click_get_count(btn) == 1))
    {
        if (g_adc_control.state == SAMPLING_IDLE) {
            adc_start_sampling();
            my_printf(&huart1, "Periodic Sampling\r\n");
            my_printf(&huart1, "sample cycle: %ds\r\n", g_adc_control.cycle_ms / 1000);

            // 记录操作日志（按照官方示例格式）
            char log_buffer[64] = {0};
            snprintf(log_buffer, sizeof(log_buffer), "sample start - cycle %ds (key press)", g_adc_control.cycle_ms / 1000);
            sd_write_log_data(log_buffer);
        } else {
            adc_stop_sampling();
            my_printf(&huart1, "Periodic Sampling STOP\r\n");

            // 记录操作日志（按照官方示例格式）
            sd_write_log_data("sample stop (key press)");
        }
    }

    // KEY2 (USER_BUTTON_2): 设置5秒周期
    if ((btn->key_id == USER_BUTTON_2) && (ebtn_click_get_count(btn) == 1))
    {
        adc_set_cycle(CYCLE_5S);
        config_set_sample_cycle(CYCLE_5S);  // 保存到配置系统
        config_save_to_flash();             // 立即保存到Flash
        my_printf(&huart1, "sample cycle adjust: 5s\r\n");

        // 记录操作日志（按照官方示例格式）
        sd_write_log_data("cycle switch to 5s (key press)");

        // 如果正在采样，重置采样时间戳以按新周期进行采样
        if (g_adc_control.state == SAMPLING_ACTIVE) {
            g_adc_control.last_sample_time = HAL_GetTick();  // 重置时间戳
        }
    }

    // KEY3 (USER_BUTTON_3): 设置10秒周期
    if ((btn->key_id == USER_BUTTON_3) && (ebtn_click_get_count(btn) == 1))
    {
        adc_set_cycle(CYCLE_10S);
        config_set_sample_cycle(CYCLE_10S); // 保存到配置系统
        config_save_to_flash();             // 立即保存到Flash
        my_printf(&huart1, "sample cycle adjust: 10s\r\n");

        // 记录操作日志（按照官方示例格式）
        sd_write_log_data("cycle switch to 10s (key press)");

        // 如果正在采样，重置采样时间戳以按新周期进行采样
        if (g_adc_control.state == SAMPLING_ACTIVE) {
            g_adc_control.last_sample_time = HAL_GetTick();  // 重置时间戳
        }
    }

    // KEY4 (USER_BUTTON_4): 设置15秒周期
    if ((btn->key_id == USER_BUTTON_4) && (ebtn_click_get_count(btn) == 1))
    {
        adc_set_cycle(CYCLE_15S);
        config_set_sample_cycle(CYCLE_15S); // 保存到配置系统
        config_save_to_flash();             // 立即保存到Flash
        my_printf(&huart1, "sample cycle adjust: 15s\r\n");

        // 记录操作日志（按照官方示例格式）
        sd_write_log_data("cycle switch to 15s (key press)");

        // 如果正在采样，重置采样时间戳以按新周期进行采样
        if (g_adc_control.state == SAMPLING_ACTIVE) {
            g_adc_control.last_sample_time = HAL_GetTick();  // 重置时间戳
        }
    }

    // 保留其他按键的原有功能
    if ((btn->key_id == USER_BUTTON_0) && (ebtn_click_get_count(btn) == 1))
    {
        
    }

    if ((btn->key_id == USER_BUTTON_5) && (ebtn_click_get_count(btn) == 1))
    {
        
    }
}

void app_btn_init(void)
{
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);
		HAL_TIM_Base_Start_IT(&htim14);
}

void  HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
		if(htim == (&htim14))
		{
			ebtn_process(uwTick);
		}
}

void btn_task(void)
{
    
}
